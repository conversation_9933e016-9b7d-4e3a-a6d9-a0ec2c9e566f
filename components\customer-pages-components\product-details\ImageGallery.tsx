import React, { useState } from 'react'
import { Dimensions, ScrollView, TouchableOpacity } from 'react-native'
import { Image, XStack, YStack } from 'tamagui'
import { SafeImage } from '../../common/SafeImage'

const { width } = Dimensions.get('window')

type Props = {
  images: string[]
}

export const ImageGallery = ({ images }: Props) => {
  const [active, setActive] = useState(0)

  return (
    <YStack gap="$2" width="100%">
      {/* Main hero image */}
      <SafeImage
        source={{ uri: images[active] }}
        style={{
          width: '100%',
          height: 400,
          borderRadius: 0,
        }}
        fallbackIcon="image"
        fallbackIconSize={80}
        fallbackIconColor="#9ca3af"
      />

      {/* Thumbnail row */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingVertical: 4 }}
      >
        <XStack gap="$2" px="$2">
          {images.map((img, idx) => (
            <TouchableOpacity key={img} onPress={() => setActive(idx)}>
              <SafeImage
                source={{ uri: img }}
                style={{
                  width: 70,
                  height: 70,
                  borderRadius: 16,
                  borderWidth: active === idx ? 2 : 0,
                  borderColor: active === idx ? "#7529B3" : "transparent",
                }}
                fallbackIcon="image"
                fallbackIconSize={30}
                fallbackIconColor="#9ca3af"
              />
            </TouchableOpacity>
          ))}
        </XStack>
      </ScrollView>
    </YStack>
  )
}
