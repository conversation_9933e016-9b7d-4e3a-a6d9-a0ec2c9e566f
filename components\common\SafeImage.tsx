import React from 'react';
import { Image, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface SafeImageProps {
  source: { uri?: string } | number;
  style?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  fallbackIcon?: keyof typeof Ionicons.glyphMap;
  fallbackIconSize?: number;
  fallbackIconColor?: string;
  fallbackBackgroundColor?: string;
}

export const SafeImage: React.FC<SafeImageProps> = ({
  source,
  style,
  resizeMode = 'cover',
  fallbackIcon = 'image-outline',
  fallbackIconSize = 24,
  fallbackIconColor = '#9ca3af',
  fallbackBackgroundColor = '#f3f4f6',
}) => {
  // Check if source is a URI and if it's empty
  const isEmptyUri = typeof source === 'object' && 
                    'uri' in source && 
                    (!source.uri || source.uri.trim() === '');

  if (isEmptyUri) {
    return (
      <View
        style={[
          style,
          {
            backgroundColor: fallbackBackgroundColor,
            justifyContent: 'center',
            alignItems: 'center',
          },
        ]}
      >
        <Ionicons
          name={fallbackIcon}
          size={fallbackIconSize}
          color={fallbackIconColor}
        />
      </View>
    );
  }

  return (
    <Image
      source={source}
      style={style}
      resizeMode={resizeMode}
    />
  );
};
