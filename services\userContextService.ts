import { getSuppliers, getUserOrders } from './apiService';
import { suppliersData } from '../temp-data/suppliersData';

export interface UserContext {
  userId?: string;
  location?: {
    city: string;
    area: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  recentOrders?: OrderSummary[];
  preferences?: UserPreferences;
  currentSession?: SessionInfo;
}

export interface OrderSummary {
  id: string;
  supplierId: string;
  supplierName: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'on_way' | 'delivered' | 'cancelled';
  total: number;
  orderDate: Date;
  estimatedDelivery?: Date;
  items: OrderItem[];
}

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
}

export interface UserPreferences {
  favoriteSuppliers: string[];
  dietaryRestrictions?: string[];
  preferredPaymentMethod?: string;
  defaultDeliveryAddress?: string;
  notificationSettings?: {
    orderUpdates: boolean;
    promotions: boolean;
    newSuppliers: boolean;
  };
}

export interface SessionInfo {
  platform: 'mobile' | 'web';
  appVersion?: string;
  lastActivity: Date;
  currentPage?: string;
}

class UserContextService {
  private userContext: UserContext = {};

  // Initialize user context (would typically load from API/storage)
  async initializeUserContext(userId?: string): Promise<UserContext> {
    // In a real app, this would fetch from API
    this.userContext = {
      userId: userId || 'demo_user',
      location: {
        city: 'Nablus',
        area: 'Downtown',
        coordinates: {
          latitude: 32.2211,
          longitude: 35.2544
        }
      },
      recentOrders: this.getMockRecentOrders(),
      preferences: {
        favoriteSuppliers: ['1', '3', '7'], // IDs from suppliersData
        preferredPaymentMethod: 'cash',
        defaultDeliveryAddress: 'Nablus Downtown, Palestine',
        notificationSettings: {
          orderUpdates: true,
          promotions: true,
          newSuppliers: false
        }
      },
      currentSession: {
        platform: 'mobile',
        appVersion: '1.0.0',
        lastActivity: new Date(),
        currentPage: 'ai-chat'
      }
    };

    return this.userContext;
  }

  // Get current user context
  getUserContext(): UserContext {
    return this.userContext;
  }

  // Update user location
  updateLocation(location: UserContext['location']) {
    this.userContext.location = location;
  }

  // Add recent order
  addRecentOrder(order: OrderSummary) {
    if (!this.userContext.recentOrders) {
      this.userContext.recentOrders = [];
    }
    this.userContext.recentOrders.unshift(order);
    // Keep only last 10 orders
    this.userContext.recentOrders = this.userContext.recentOrders.slice(0, 10);
  }

  // Get suppliers near user
  getNearbySuppliers(category?: string): any[] {
    const allSuppliers = suppliersData;
    
    // Filter by category if provided
    let filteredSuppliers = category 
      ? allSuppliers.filter(supplier => supplier.category === category)
      : allSuppliers;

    // In a real app, this would filter by actual distance
    // For now, we'll just return all suppliers
    return filteredSuppliers.slice(0, 10); // Limit to 10 for performance
  }

  // Get user's favorite suppliers
  getFavoriteSuppliers(): any[] {
    const favorites = this.userContext.preferences?.favoriteSuppliers || [];
    return suppliersData.filter(supplier => favorites.includes(supplier.id));
  }

  // Get current promotions relevant to user
  getRelevantPromotions(): any[] {
    const userLocation = this.userContext.location?.city || '';
    const favoriteSuppliers = this.userContext.preferences?.favoriteSuppliers || [];
    
    return promotions.filter(promo => {
      // Filter promotions by location or favorite suppliers
      return promo.location?.includes(userLocation) || 
             favoriteSuppliers.includes(promo.supplierId);
    });
  }

  // Get order tracking information
  getOrderTrackingInfo(orderId?: string): OrderSummary | OrderSummary[] | null {
    const recentOrders = this.userContext.recentOrders || [];
    
    if (orderId) {
      return recentOrders.find(order => order.id === orderId) || null;
    }
    
    // Return active orders (not delivered or cancelled)
    return recentOrders.filter(order => 
      !['delivered', 'cancelled'].includes(order.status)
    );
  }

  // Generate context summary for AI
  getContextSummary(): string {
    const context = this.userContext;
    let summary = "User Context:\n";

    if (context.location) {
      summary += `• Location: ${context.location.city}, ${context.location.area}\n`;
    }

    if (context.recentOrders && context.recentOrders.length > 0) {
      const activeOrders = context.recentOrders.filter(order => 
        !['delivered', 'cancelled'].includes(order.status)
      );
      
      if (activeOrders.length > 0) {
        summary += `• Active Orders: ${activeOrders.length} order(s) in progress\n`;
        activeOrders.forEach(order => {
          summary += `  - Order #${order.id}: ${order.supplierName} (${order.status})\n`;
        });
      }

      summary += `• Recent Orders: ${context.recentOrders.length} total orders\n`;
    }

    if (context.preferences?.favoriteSuppliers) {
      const favoriteNames = this.getFavoriteSuppliers().map(s => s.name).join(', ');
      summary += `• Favorite Suppliers: ${favoriteNames}\n`;
    }

    const relevantPromotions = this.getRelevantPromotions();
    if (relevantPromotions.length > 0) {
      summary += `• Available Promotions: ${relevantPromotions.length} relevant offers\n`;
    }

    return summary;
  }

  // Mock recent orders for demo
  private getMockRecentOrders(): OrderSummary[] {
    return [
      {
        id: 'ORD001',
        supplierId: '1',
        supplierName: 'Al-Quds Restaurant',
        status: 'on_way',
        total: 45.50,
        orderDate: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        estimatedDelivery: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes from now
        items: [
          { id: '1', name: 'Mansaf', quantity: 1, price: 25.00 },
          { id: '2', name: 'Hummus', quantity: 2, price: 8.00 },
          { id: '3', name: 'Fresh Juice', quantity: 2, price: 6.50 }
        ]
      },
      {
        id: 'ORD002',
        supplierId: '3',
        supplierName: 'Fresh Market',
        status: 'delivered',
        total: 32.75,
        orderDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        items: [
          { id: '4', name: 'Fresh Vegetables', quantity: 1, price: 15.00 },
          { id: '5', name: 'Bread', quantity: 3, price: 4.50 },
          { id: '6', name: 'Milk', quantity: 2, price: 6.75 }
        ]
      },
      {
        id: 'ORD003',
        supplierId: '7',
        supplierName: 'City Pharmacy',
        status: 'delivered',
        total: 18.25,
        orderDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        items: [
          { id: '7', name: 'Pain Relief', quantity: 1, price: 12.00 },
          { id: '8', name: 'Vitamins', quantity: 1, price: 6.25 }
        ]
      }
    ];
  }

  // Get smart suggestion keys based on user context
  // These keys will be translated in the component that uses them
  getSmartSuggestionKeys(): string[] {
    const suggestionKeys: string[] = [];
    const context = this.userContext;

    // Suggest based on active orders
    const activeOrders = this.getOrderTrackingInfo() as OrderSummary[];
    if (Array.isArray(activeOrders) && activeOrders.length > 0) {
      suggestionKeys.push("chat.contextSuggestions.trackCurrentOrder");
      suggestionKeys.push("chat.contextSuggestions.contactDriver");
    }

    // Suggest based on time of day
    const hour = new Date().getHours();
    if (hour >= 11 && hour <= 14) {
      suggestionKeys.push("chat.contextSuggestions.findLunchOptions");
      suggestionKeys.push("chat.contextSuggestions.orderFromFavorite");
    } else if (hour >= 17 && hour <= 21) {
      suggestionKeys.push("chat.contextSuggestions.browseDinnerMenus");
      suggestionKeys.push("chat.contextSuggestions.orderGroceriesTonight");
    }

    // Suggest based on location
    if (context.location?.city === 'Nablus') {
      suggestionKeys.push("chat.contextSuggestions.findSuppliersNearMe");
      suggestionKeys.push("chat.contextSuggestions.checkLocalPromotions");
    }

    // Suggest based on recent activity
    const recentOrders = context.recentOrders || [];
    if (recentOrders.length === 0) {
      suggestionKeys.push("chat.contextSuggestions.howToPlaceFirstOrder");
      suggestionKeys.push("chat.contextSuggestions.browsePopularSuppliers");
    }

    return suggestionKeys.slice(0, 4); // Return top 4 suggestion keys
  }

  // Legacy method for backward compatibility - returns translated suggestions
  getSmartSuggestions(): string[] {
    // This method is kept for backward compatibility but should use translation keys
    // The actual translation should be done in the component
    return this.getSmartSuggestionKeys();
  }
}

export const userContextService = new UserContextService();
