import { useState, useEffect } from 'react';
import { Dimensions, Pressable, Alert } from 'react-native';
import { View, Text, Card, Button, YStack } from 'tamagui';
import { MotiView } from 'moti';
import { getSuppliers, Supplier as BackendSupplier } from '../../services/apiService';
import { router } from 'expo-router';
import { Image } from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import * as Location from 'expo-location';

type Addition = { id: string; name: string; price: number }

type Product = {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
    restaurantOptions?: {
      additions?: Addition[];
      without?: string[];
      sides?: Addition[];
    };
    clothingOptions?: {
      sizes: string[];
      colors: string[];
      gallery: string[];
    };
  }

// Use the backend Supplier type with coordinates
type Supplier = BackendSupplier & {
  lat: number;
  lng: number;
};

export default function SuppliersMap() {
  const { width, height } = Dimensions.get('window');
  const [selected, setSelected] = useState<Supplier | null>(null);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentRegion, setCurrentRegion] = useState({
    latitude: 32.2211,
    longitude: 35.2544,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  // Fetch suppliers from backend
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        setLoading(true);
        setError(null);
        const { suppliers: backendSuppliers } = await getSuppliers();

        // Transform backend suppliers to include coordinates
        const suppliersWithCoords: Supplier[] = backendSuppliers.map(supplier => {
          // Ensure we have valid coordinates or use defaults
          const hasValidCoordinates = supplier.location &&
                                     Array.isArray(supplier.location.coordinates) &&
                                     supplier.location.coordinates.length >= 2;

          return {
            ...supplier,
            lat: hasValidCoordinates ? supplier.location.coordinates[1] : 32.2211, // Default to Nablus if no coordinates
            lng: hasValidCoordinates ? supplier.location.coordinates[0] : 35.2544,
          };
        });

        setSuppliers(suppliersWithCoords);
      } catch (err) {
        console.error('Error fetching suppliers:', err);
        setError('Failed to load suppliers');
        Alert.alert('Error', 'Failed to load suppliers. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchSuppliers();
  }, []);

  // Get user's current location on component mount
  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission denied, using default location (Nablus)');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      setCurrentRegion({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      });
    } catch (error) {
      console.error('Error getting current location:', error);
      // Keep default location (Nablus) if location fails
    }
  };

  if (loading) {
    return (
      <View flex={1} justifyContent="center" alignItems="center">
        <Text>Loading suppliers...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View flex={1} justifyContent="center" alignItems="center">
        <Text color="red">{error}</Text>
        <Button onPress={() => window.location.reload()} style={{ marginTop: 16 }}>
          Retry
        </Button>
      </View>
    );
  }

  return (
    <View flex={1}>
      {/* Real Map */}
      <MapView
        style={{ width, height }}
        region={currentRegion}
        showsUserLocation={true}
        showsMyLocationButton={true}
        onRegionChangeComplete={setCurrentRegion}
      >
        {/* Supplier Markers */}
        {suppliers.map((sup, index) => (
          <Marker
            key={sup.id}
            coordinate={{
              latitude: sup.lat,
              longitude: sup.lng,
            }}
            onPress={() => setSelected(sup)}
          >
            <View
              style={{
                width: 50,
                height: 50,
                borderRadius: 25,
                overflow: 'hidden',
                borderWidth: 2,
                borderColor: '#fff',
                backgroundColor: '#eee',
                justifyContent: 'center',
                alignItems: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 3,
                elevation: 5,
              }}
            >
              <Image
                source={{ uri: sup.logoUrl }}
                style={{ width: '100%', height: '100%' }}
                resizeMode="cover"
              />
            </View>
          </Marker>
        ))}
      </MapView>

      {selected && (
        <MotiView
          from={{ opacity: 0, translateY: 50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing' }}
          style={{ position: 'absolute', bottom: 80, left: 20, right: 20 }}
        >
          <Card p="$4" br="$6" elevate bw={1} bc="$gray5" bg="white">
            <YStack gap="$2">
              <Text fontSize="$6" fontWeight="700">{selected.name}</Text>
              <Text fontSize="$4" color="$gray9">{selected.category}</Text>
              <Button onPress={() => router.push({
                    pathname: "/home/<USER>",
                    params: { supplierId: selected.id }
              })}>
                View
              </Button>
              <Button variant="outlined" onPress={() => setSelected(null)}>Close</Button>
            </YStack>
          </Card>
        </MotiView>
      )}
    </View>
  );
}
