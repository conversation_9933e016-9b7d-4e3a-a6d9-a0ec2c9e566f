import React from 'react';
import { Alert } from 'react-native';
import { YStack, XStack, Text, H4, Card } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { getErrorMessage, UserFriendlyError } from '../../utils/errorHandler';

interface ErrorDisplayProps {
  error: any;
  onDismiss?: () => void;
  variant?: 'alert' | 'inline' | 'toast';
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onDismiss,
  variant = 'alert'
}) => {
  const { t } = useTranslation();
  
  if (!error) return null;

  const errorInfo: UserFriendlyError = getErrorMessage(error, t);

  if (variant === 'alert') {
    Alert.alert(
      errorInfo.title,
      errorInfo.message,
      [
        {
          text: t('common.ok', { defaultValue: 'OK' }),
          onPress: onDismiss
        }
      ]
    );
    return null;
  }

  const getIconName = (type: string) => {
    switch (type) {
      case 'warning':
        return 'warning-outline';
      case 'info':
        return 'information-circle-outline';
      default:
        return 'alert-circle-outline';
    }
  };

  const getIconColor = (type: string) => {
    switch (type) {
      case 'warning':
        return '#f59e0b';
      case 'info':
        return '#3b82f6';
      default:
        return '#ef4444';
    }
  };

  const getBackgroundColor = (type: string) => {
    switch (type) {
      case 'warning':
        return '#fef3c7';
      case 'info':
        return '#dbeafe';
      default:
        return '#fee2e2';
    }
  };

  const getBorderColor = (type: string) => {
    switch (type) {
      case 'warning':
        return '#f59e0b';
      case 'info':
        return '#3b82f6';
      default:
        return '#ef4444';
    }
  };

  if (variant === 'inline') {
    return (
      <Card
        padding="$4"
        marginVertical="$2"
        backgroundColor={getBackgroundColor(errorInfo.type)}
        borderWidth={1}
        borderColor={getBorderColor(errorInfo.type)}
        borderRadius="$4"
      >
        <XStack alignItems="flex-start" gap="$3">
          <Ionicons
            name={getIconName(errorInfo.type)}
            size={20}
            color={getIconColor(errorInfo.type)}
          />
          <YStack flex={1} gap="$1">
            <H4 color={getIconColor(errorInfo.type)} fontSize="$5" fontWeight="600">
              {errorInfo.title}
            </H4>
            <Text color={getIconColor(errorInfo.type)} fontSize="$3" lineHeight="$4">
              {errorInfo.message}
            </Text>
          </YStack>
          {onDismiss && (
            <Ionicons
              name="close"
              size={18}
              color={getIconColor(errorInfo.type)}
              onPress={onDismiss}
              style={{ padding: 4 }}
            />
          )}
        </XStack>
      </Card>
    );
  }

  // Toast variant would be implemented with a toast library
  return null;
};

// Utility function to show professional error alerts
export const showErrorAlert = (error: any, t: any, onDismiss?: () => void) => {
  const errorInfo: UserFriendlyError = getErrorMessage(error, t);
  
  Alert.alert(
    errorInfo.title,
    errorInfo.message,
    [
      {
        text: t('common.ok', { defaultValue: 'OK' }),
        onPress: onDismiss
      }
    ]
  );
};
