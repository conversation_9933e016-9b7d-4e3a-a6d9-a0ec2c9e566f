import React, { useState } from 'react';
import { YStack, Button, Text } from 'tamagui';
import { ErrorDisplay } from './ErrorDisplay';

// Example component showing how to use ErrorDisplay
export const ErrorDisplayExample: React.FC = () => {
  const [error, setError] = useState<any>(null);

  const simulateErrors = () => {
    const errors = [
      { message: 'Invalid email or password' },
      { message: 'Email already exists' },
      { message: 'Username already taken' },
      { message: 'Network error occurred' },
      { message: 'Server error' },
      { message: 'Account not verified' },
    ];
    
    const randomError = errors[Math.floor(Math.random() * errors.length)];
    setError(randomError);
  };

  return (
    <YStack gap="$4" padding="$4">
      <Text fontSize="$6" fontWeight="bold">Error Display Examples</Text>
      
      <Button onPress={simulateErrors}>
        Simulate Random Error
      </Button>
      
      {error && (
        <ErrorDisplay
          error={error}
          variant="inline"
          onDismiss={() => setError(null)}
        />
      )}
    </YStack>
  );
};
