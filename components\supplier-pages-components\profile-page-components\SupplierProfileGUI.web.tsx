import { useEffect, useState } from 'react';
import { Sc<PERSON>View, <PERSON>dal, Alert, Dimensions } from 'react-native';
import { View, Text, Button, Input, YStack, XStack, Card, H4, Separator } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { useAutoInitializedUser } from '~/hooks/useAutoInitializedUser';
import { useRouter } from 'expo-router';
import { apiService } from '~/services/apiService';
import * as Location from 'expo-location';
// Use react-native-web-maps directly for web
import MapView, { Marker } from 'react-native-web-maps';

export default function SupplierProfileGUI() {
  const { user, setCurrentUser } = useAutoInitializedUser();
  const router = useRouter();
  const { width, height } = Dimensions.get('window');

  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phoneNumber || '',
    storeName: user?.storeName || '',
    storeDescription: '', // This property doesn't exist in User type
    category: user?.businessType || '',
  });

  const [location, setLocation] = useState<[number, number]>([35.2544, 32.2211]); // [lng, lat] - Nablus, Palestine
  const [mapFullscreen, setMapFullscreen] = useState(false);

  useEffect(() => {
    if (user?.location?.coordinates) {
      setLocation(user.location.coordinates);
    } else {
      getCurrentLocation();
    }
  }, [user]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') return;

      const location = await Location.getCurrentPositionAsync({});
      setLocation([location.coords.longitude, location.coords.latitude]);
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const handleSave = async () => {
    try {
      const response = await apiService.updateProfile({
        firstName: formData.firstName,
        lastName: formData.lastName,
        phoneNumber: formData.phone,
        // Note: storeLocation and other supplier-specific fields may need separate API endpoints
      });

      if (response.success && response.data) {
        setCurrentUser(response.data.user);
        setIsEditing(false);
        Alert.alert('Success', 'Profile updated successfully');
      } else {
        Alert.alert('Error', response.message || 'Failed to update profile');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await apiService.logout();
              router.replace('/');
            } catch (error) {
              console.error('Logout error:', error);
              router.replace('/');
            }
          }
        },
      ]
    );
  };

  return (
    <>
      <ScrollView style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
        <YStack p="$4" gap="$4">
          {/* Profile Header */}
          <Card p="$4" br="$4" bg="white">
            <XStack justifyContent="space-between" alignItems="center">
              <H4>Store Profile</H4>
              <Button
                size="$3"
                variant="outlined"
                onPress={() => setIsEditing(!isEditing)}
                icon={<Ionicons name={isEditing ? "close" : "pencil"} size={16} />}
              >
                {isEditing ? 'Cancel' : 'Edit'}
              </Button>
            </XStack>
          </Card>

          {/* Personal Information */}
          <Card p="$4" br="$4" bg="white">
            <YStack gap="$3">
              <Text fontSize="$5" fontWeight="600">Personal Information</Text>
              <Separator />
              
              <YStack gap="$3">
                <XStack gap="$3">
                  <YStack flex={1}>
                    <Text fontSize="$3" color="$gray10" mb="$1">First Name</Text>
                    <Input
                      value={formData.firstName}
                      onChangeText={(text) => setFormData(prev => ({ ...prev, firstName: text }))}
                      editable={isEditing}
                      bg={isEditing ? "white" : "$gray2"}
                    />
                  </YStack>
                  <YStack flex={1}>
                    <Text fontSize="$3" color="$gray10" mb="$1">Last Name</Text>
                    <Input
                      value={formData.lastName}
                      onChangeText={(text) => setFormData(prev => ({ ...prev, lastName: text }))}
                      editable={isEditing}
                      bg={isEditing ? "white" : "$gray2"}
                    />
                  </YStack>
                </XStack>

                <YStack>
                  <Text fontSize="$3" color="$gray10" mb="$1">Email</Text>
                  <Input
                    value={formData.email}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
                    editable={false}
                    bg="$gray2"
                  />
                </YStack>

                <YStack>
                  <Text fontSize="$3" color="$gray10" mb="$1">Phone</Text>
                  <Input
                    value={formData.phone}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, phone: text }))}
                    editable={isEditing}
                    bg={isEditing ? "white" : "$gray2"}
                  />
                </YStack>
              </YStack>
            </YStack>
          </Card>

          {/* Store Information */}
          <Card p="$4" br="$4" bg="white">
            <YStack gap="$3">
              <Text fontSize="$5" fontWeight="600">Store Information</Text>
              <Separator />
              
              <YStack gap="$3">
                <YStack>
                  <Text fontSize="$3" color="$gray10" mb="$1">Store Name</Text>
                  <Input
                    value={formData.storeName}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, storeName: text }))}
                    editable={isEditing}
                    bg={isEditing ? "white" : "$gray2"}
                  />
                </YStack>

                <YStack>
                  <Text fontSize="$3" color="$gray10" mb="$1">Category</Text>
                  <Input
                    value={formData.category}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, category: text }))}
                    editable={isEditing}
                    bg={isEditing ? "white" : "$gray2"}
                  />
                </YStack>

                <YStack>
                  <Text fontSize="$3" color="$gray10" mb="$1">Description</Text>
                  <Input
                    value={formData.storeDescription}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, storeDescription: text }))}
                    editable={isEditing}
                    bg={isEditing ? "white" : "$gray2"}
                    multiline
                    numberOfLines={3}
                  />
                </YStack>
              </YStack>
            </YStack>
          </Card>

          {/* Store Location */}
          <Card p="$4" br="$4" bg="white">
            <YStack gap="$3">
              <XStack justifyContent="space-between" alignItems="center">
                <Text fontSize="$5" fontWeight="600">Store Location</Text>
                {isEditing && (
                  <Button
                    size="$3"
                    variant="outlined"
                    onPress={() => setMapFullscreen(true)}
                    icon={<Ionicons name="map" size={16} />}
                  >
                    Edit Location
                  </Button>
                )}
              </XStack>
              <Separator />
              
              <View style={{ height: 200, borderRadius: 8, overflow: 'hidden' }}>
                <MapView
                  style={{ width: '100%', height: '100%' }}
                  region={{
                    latitude: location[1],
                    longitude: location[0],
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  }}
                  scrollEnabled={false}
                  zoomEnabled={false}
                >
                  <Marker
                    coordinate={{
                      latitude: location[1],
                      longitude: location[0],
                    }}
                    pinColor="red"
                    title="Store Location"
                  />
                </MapView>
              </View>
            </YStack>
          </Card>

          {/* Action Buttons */}
          {isEditing && (
            <Card p="$4" br="$4" bg="white">
              <XStack gap="$3">
                <Button
                  flex={1}
                  bg="$green9"
                  color="white"
                  onPress={handleSave}
                  icon={<Ionicons name="checkmark" size={16} color="white" />}
                >
                  Save Changes
                </Button>
              </XStack>
            </Card>
          )}

          <Card p="$4" br="$4" bg="white">
            <Button
              bg="$red9"
              color="white"
              onPress={handleLogout}
              icon={<Ionicons name="log-out" size={16} color="white" />}
            >
              Logout
            </Button>
          </Card>
        </YStack>
      </ScrollView>

      {/* Full Map Modal */}
      <Modal visible={mapFullscreen} animationType="slide">
        <YStack f={1}>
            <MapView
              style={{ flex: 1 }}
              region={{
                latitude: location[1],
                longitude: location[0],
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              }}
              onPress={(e) => {
                const { latitude, longitude } = e.nativeEvent.coordinate;
                setLocation([longitude, latitude]);
              }}
            >
                <Marker
                  coordinate={{
                    latitude: location[1],
                    longitude: location[0],
                  }}
                  pinColor="red"
                  title="Store Location"
                />
            </MapView>
            <Button
            bg="$primary"
            color="white"
            br={100}
            position="absolute"
            top={40}
            right={20}
            zIndex={1000}
            icon={<Ionicons name="close" size={20} color="white" />}
            onPress={() => setMapFullscreen(false)}
            />
        </YStack>
        </Modal>
    </>
  )
}
