import { create } from 'zustand';
import { apiService } from '../services/apiService';

// Define User type locally since it's not exported from apiService
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  username: string;
  phoneNumber: string;
  role: 'customer' | 'supplier';
  isEmailVerified: boolean;
  address?: string;
  city?: string;
  country?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  isActive: boolean;
  notifications?: boolean;
  location?: [number, number];
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
}

type UserStore = {
    user: User | null;
    isLoading: boolean;
    error: string | null;
    setCurrentUser: (user: User | null) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    clearUser: () => void;
    loadUserProfile: () => Promise<void>;
}

export const useCurrentUserData = create<UserStore>((set, get) => ({
    user: null,
    isLoading: false,
    error: null,

    setCurrentUser: (user) => set({ user, error: null }),
    setLoading: (isLoading) => set({ isLoading }),
    setError: (error) => set({ error }),
    clearUser: () => set({ user: null, error: null }),

    loadUserProfile: async () => {
        if (!apiService.isAuthenticated()) {
            set({ user: null, error: null });
            return;
        }

        set({ isLoading: true, error: null });

        try {
            const response = await apiService.getProfile();

            if (response.success && response.data) {
                set({ user: response.data.user, isLoading: false, error: null });
            } else {
                set({ user: null, isLoading: false, error: response.message });
            }
        } catch (error) {
            set({
                user: null,
                isLoading: false,
                error: error instanceof Error ? error.message : 'Failed to load profile'
            });
        }
    },
}));