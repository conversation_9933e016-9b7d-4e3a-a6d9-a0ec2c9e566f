import AsyncStorage from '@react-native-async-storage/async-storage';
import { getApiBaseUrl } from './networkUtils';

// Types
export interface Service {
  _id: string;
  key: string;
  label: string;
  icon: string;
  color: string;
  route: string;
  isActive: boolean;
  order: number;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  _id: string;
  key: string;
  label: string;
  icon: string;
  color: string;
  route: {
    pathname: string;
    params: {
      category: string;
    };
  };
  isActive: boolean;
  order: number;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id: string;
  name: string;
  image: string;
  price: number;
  discountPrice?: number;
  category: string;
  description?: string;
  isAvailable: boolean;
  restaurantOptions?: {
    additions?: Array<{ id: string; name: string; price: number }>;
    without?: string[];
    sides?: Array<{ id: string; name: string; price: number }>;
  };
  clothingOptions?: {
    sizes: string[];
    colors: string[];
    gallery: string[];
  };
}

export interface Supplier {
  _id: string;
  id: string;
  name: string;
  lat: number;
  lng: number;
  category: string;
  rating: number;
  tags: string[];
  logoUrl: string;
  banner: string;
  openHours: string;
  deliveryTime: string;
  phone: string;
  address?: string;
  description?: string;
  isActive: boolean;
  products: Product[];
  createdAt: string;
  updatedAt: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ApiError {
  success: false;
  message: string;
  error?: any;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  // Basic Information
  firstName: string;
  lastName: string;
  email: string;

  // Contact & Security
  phoneNumber: string;
  password: string;

  // Profile Setup
  username: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';

  // Address Information
  address: string;
  city: string;
  country: string;

  // Account Type
  role: 'customer' | 'supplier';

  // Business Information (for suppliers)
  supplierId?: string;
  storeName?: string;
  businessType?: 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other';
  openHours?: string;

  // Location & Preferences
  location?: [number, number]; // [longitude, latitude]
  notifications?: boolean;
}

export interface AuthResponse {
  user: any;
  accessToken: string;
  refreshToken: string;
}

// Base API class
class ApiService {
  private baseURL: string;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private refreshPromise: Promise<boolean> | null = null;

  constructor() {
    this.baseURL = getApiBaseUrl();
    // Load tokens from storage on initialization
    this.loadTokensFromStorage();
  }

  private async loadTokensFromStorage() {
    try {
      this.accessToken = await AsyncStorage.getItem('accessToken');
      this.refreshToken = await AsyncStorage.getItem('refreshToken');
      console.log('🔑 Tokens loaded from storage:', {
        hasAccessToken: !!this.accessToken,
        hasRefreshToken: !!this.refreshToken,
        accessTokenLength: this.accessToken?.length || 0,
        refreshTokenLength: this.refreshToken?.length || 0
      });
    } catch (error) {
      console.error('❌ Error loading tokens from storage:', error);
    }
  }

  // Public method to refresh tokens from storage
  async refreshTokensFromStorage() {
    await this.loadTokensFromStorage();
  }

  private async saveTokensToStorage(accessToken: string, refreshToken: string) {
    try {
      this.accessToken = accessToken;
      this.refreshToken = refreshToken;

      await AsyncStorage.setItem('accessToken', accessToken);
      await AsyncStorage.setItem('refreshToken', refreshToken);
      console.log('✅ Tokens saved to storage successfully');
      console.log('🔑 New tokens set in memory:', {
        accessTokenLength: this.accessToken?.length || 0,
        refreshTokenLength: this.refreshToken?.length || 0
      });
    } catch (error) {
      console.error('❌ Error saving tokens to storage:', error);
    }
  }

  private async clearTokensFromStorage() {
    try {
      this.accessToken = null;
      this.refreshToken = null;

      await AsyncStorage.removeItem('accessToken');
      await AsyncStorage.removeItem('refreshToken');
      console.log('🗑️ Tokens cleared from storage');
    } catch (error) {
      console.error('❌ Error clearing tokens from storage:', error);
    }
  }

  private async refreshAccessToken(): Promise<boolean> {
    // If a refresh is already in progress, wait for it
    if (this.refreshPromise) {
      console.log('🔄 Token refresh already in progress, waiting...');
      return await this.refreshPromise;
    }

    if (!this.refreshToken) {
      console.log('❌ No refresh token available');
      return false;
    }

    // Create a new refresh promise
    this.refreshPromise = this.performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      // Clear the promise when done
      this.refreshPromise = null;
    }
  }

  private async performTokenRefresh(): Promise<boolean> {
    try {
      console.log('🔄 Attempting to refresh token...');
      const response = await fetch(`${this.baseURL}/auth/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken: this.refreshToken }),
      });

      console.log(`📥 Refresh response status: ${response.status}`);
      const data = await response.json();
      console.log('📦 Refresh response data:', data);

      if (response.ok && data.success && data.data) {
        console.log('✅ Token refresh successful');
        await this.saveTokensToStorage(data.data.accessToken, data.data.refreshToken);
        return true;
      } else {
        console.log('❌ Token refresh failed:', data.message || 'Unknown error');
        await this.clearTokensFromStorage();
        return false;
      }
    } catch (error) {
      console.error('❌ Token refresh error:', error);
      await this.clearTokensFromStorage();
      return false;
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const config: RequestInit = {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      };

      // Add authorization header if token exists
      if (this.accessToken) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${this.accessToken}`,
        };
        console.log(`🔐 Using access token: ${this.accessToken.substring(0, 20)}... (length: ${this.accessToken.length})`);
        console.log(`🔐 Refresh token available: ${this.refreshToken ? 'Yes (length: ' + this.refreshToken.length + ')' : 'No'}`);
      } else {
        console.log(`⚠️ No access token available for request to ${endpoint}`);
        console.log(`🔐 Refresh token available: ${this.refreshToken ? 'Yes (length: ' + this.refreshToken.length + ')' : 'No'}`);
      }

      console.log(`📤 Making request to: ${url}`);
      const response = await fetch(url, config);
      console.log(`📥 Response status: ${response.status}`);

      const data = await response.json();
      console.log(`📦 Response data:`, data);

      // If token is expired, try to refresh
      if (response.status === 401 && this.refreshToken) {
        console.log(`🔄 Token expired, attempting refresh...`);
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          console.log(`✅ Token refreshed successfully, retrying original request...`);
          // Retry the original request with new token
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${this.accessToken}`,
          };
          const retryResponse = await fetch(url, config);
          const retryData = await retryResponse.json();
          console.log(`📦 Retry response:`, retryData);

          if (!retryResponse.ok) {
            const errorMessage = retryData.message || `Request failed with status ${retryResponse.status}`;
            throw new Error(errorMessage);
          }

          return retryData;
        } else {
          console.log(`❌ Token refresh failed, user needs to login again`);
          await this.clearTokensFromStorage();
        }
      }

      if (!response.ok) {
        // Extract meaningful error message from response
        const errorMessage = data.message || `Request failed with status ${response.status}`;
        throw new Error(errorMessage);
      }

      return data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);

      // Enhance error message for better user experience
      if (error instanceof Error) {
        // Network errors
        if (error.message.includes('fetch') || error.message.includes('Network request failed')) {
          throw new Error('Please check your internet connection and try again');
        }
        // Keep the original error message if it's already meaningful
        throw error;
      }

      throw new Error('An unexpected error occurred. Please try again.');
    }
  }

  // Services API
  async getServices(): Promise<Service[]> {
    const response = await this.request<Service[]>('/services');
    return response.data;
  }

  async getServiceByKey(key: string): Promise<Service> {
    const response = await this.request<Service>(`/services/${key}`);
    return response.data;
  }

  // Categories API
  async getCategories(): Promise<Category[]> {
    const response = await this.request<Category[]>('/categories');
    return response.data;
  }

  async getCategoryByKey(key: string): Promise<Category> {
    const response = await this.request<Category>(`/categories/${key}`);
    return response.data;
  }

  // Suppliers API
  async getSuppliers(params?: {
    category?: string;
    search?: string;
    lat?: number;
    lng?: number;
    radius?: number;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ suppliers: Supplier[]; pagination?: any }> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/suppliers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await this.request<Supplier[]>(endpoint);
    
    return {
      suppliers: response.data,
      pagination: response.pagination
    };
  }

  async getSuppliersByCategory(
    category: string,
    params?: {
      search?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<{ suppliers: Supplier[]; pagination?: any }> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/suppliers/category/${category}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await this.request<Supplier[]>(endpoint);
    
    return {
      suppliers: response.data,
      pagination: response.pagination
    };
  }

  async getSupplierById(id: string): Promise<Supplier> {
    const response = await this.request<Supplier>(`/suppliers/${id}`);
    return response.data;
  }

  async getSupplierProducts(
    id: string,
    params?: {
      category?: string;
      search?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<{
    supplier: {
      id: string;
      name: string;
      rating: number;
      deliveryTime: string;
      openHours: string;
    };
    products: Product[];
    categories: string[];
    pagination?: any;
  }> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/suppliers/${id}/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await this.request<{
      supplier: any;
      products: Product[];
      categories: string[];
      pagination?: any;
    }>(endpoint);
    
    return response.data;
  }

  async getProductById(supplierId: string, productId: string): Promise<{
    supplier: {
      id: string;
      name: string;
      rating: number;
      deliveryTime: string;
    };
    product: Product;
  }> {
    const response = await this.request<{
      supplier: any;
      product: Product;
    }>(`/suppliers/${supplierId}/products/${productId}`);

    return response.data;
  }

  // Orders API
  async createOrder(orderData: {
    supplierId: string;
    supplierName: string;
    items: Array<{
      productId: string;
      productName: string;
      quantity: number;
      price: number;
      options?: any;
    }>;
    subtotal: number;
    deliveryFee: number;
    totalAmount: number;
    paymentMethod: 'cash' | 'card' | 'wallet';
    deliveryAddress: {
      street: string;
      city: string;
      coordinates: {
        lat: number;
        lng: number;
      };
      notes?: string;
    };
    notes?: string;
  }): Promise<any> {
    const response = await this.request<any>('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
    return response.data;
  }

  async getUserOrders(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<{ orders: any[]; pagination?: any }> {
    try {
      const queryParams = new URLSearchParams();

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString());
          }
        });
      }

      const endpoint = `/orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await this.request<any[]>(endpoint);

      return {
        orders: response.data,
        pagination: response.pagination
      };
    } catch (error) {
      console.error('Error fetching orders:', error);
      // Return empty orders array if authentication fails or other errors occur
      return {
        orders: [],
        pagination: undefined
      };
    }
  }

  async getOrderById(orderId: string): Promise<any> {
    const response = await this.request<any>(`/orders/${orderId}`);
    return response.data;
  }

  async updateOrderStatus(orderId: string, status: string, trackingUpdate?: {
    message: string;
    location?: { lat: number; lng: number };
  }): Promise<any> {
    const response = await this.request<any>(`/orders/${orderId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status, trackingUpdate }),
    });
    return response.data;
  }

  // Packages API
  async createPackage(packageData: {
    recipientInfo: {
      name: string;
      phone: string;
    };
    pickupAddress: {
      street: string;
      city: string;
      coordinates: {
        lat: number;
        lng: number;
      };
    };
    deliveryAddress: {
      street: string;
      city: string;
      coordinates: {
        lat: number;
        lng: number;
      };
    };
    packageDetails: {
      type: string;
      size: 'small' | 'medium' | 'large';
      weight: string;
    };
    priority?: 'standard' | 'express' | 'urgent';
    paymentMethod: 'cash' | 'card' | 'wallet';
    scheduledPickupTime?: string;
    notes?: string;
  }): Promise<any> {
    const response = await this.request<any>('/packages', {
      method: 'POST',
      body: JSON.stringify(packageData),
    });
    return response.data;
  }

  async requestPickup(pickupData: {
    pickupAddress: {
      street: string;
      city: string;
      coordinates: {
        lat: number;
        lng: number;
      };
    };
    packageDetails: {
      type: string;
      size: 'small' | 'medium' | 'large';
      weight: string;
    };
    preferredTime?: string;
    notes?: string;
  }): Promise<any> {
    const response = await this.request<any>('/packages/request-pickup', {
      method: 'POST',
      body: JSON.stringify(pickupData),
    });
    return response.data;
  }

  async getUserPackages(params?: {
    page?: number;
    limit?: number;
    status?: string;
    type?: string;
  }): Promise<{ packages: any[]; pagination?: any }> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/packages${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await this.request<any[]>(endpoint);

    return {
      packages: response.data,
      pagination: response.pagination
    };
  }

  async getPackageByTrackingNumber(trackingNumber: string): Promise<any> {
    const response = await this.request<any>(`/packages/${trackingNumber}`);
    return response.data;
  }

  async updatePackageStatus(trackingNumber: string, status: string, trackingUpdate?: {
    message: string;
    location?: { lat: number; lng: number };
  }): Promise<any> {
    const response = await this.request<any>(`/packages/${trackingNumber}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status, trackingUpdate }),
    });
    return response.data;
  }

  // Authentication methods
  async login(email: string, password: string): Promise<ApiResponse<any>> {
    console.log('🔐 Attempting login for:', email);
    const response = await this.request<any>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    if (response.success && response.data) {
      console.log('✅ Login successful, saving tokens...');
      await this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
    }

    return response;
  }

  async signup(userData: any): Promise<ApiResponse<any>> {
    console.log('📝 Attempting signup for:', userData.email);
    const response = await this.request<any>('/auth/signup', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    if (response.success && response.data) {
      console.log('✅ Signup successful, saving tokens...');
      await this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
    }

    return response;
  }

  async logout(): Promise<void> {
    console.log('🚪 Logging out...');
    try {
      if (this.refreshToken) {
        await this.request('/auth/logout', {
          method: 'POST',
          body: JSON.stringify({ refreshToken: this.refreshToken }),
        });
      }
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      await this.clearTokensFromStorage();
    }
  }

  async verifyEmail(token: string): Promise<ApiResponse<any>> {
    return await this.request<any>('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify({ token }),
    });
  }

  async resendVerificationEmail(email: string): Promise<ApiResponse<any>> {
    return await this.request<any>('/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async forgotPassword(email: string): Promise<ApiResponse<any>> {
    return await this.request<any>('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async verifyResetCode(email: string, code: string): Promise<ApiResponse<any>> {
    return await this.request<any>('/auth/verify-reset-code', {
      method: 'POST',
      body: JSON.stringify({ email, code }),
    });
  }

  async resetPassword(email: string, code: string, newPassword: string): Promise<ApiResponse<any>> {
    return await this.request<any>('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({ email, code, newPassword }),
    });
  }

  async getCurrentUser(): Promise<ApiResponse<any>> {
    return await this.request<any>('/auth/me');
  }

  async updateProfile(userData: any): Promise<ApiResponse<any>> {
    return await this.request<any>('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  // Utility methods
  async isAuthenticated(): Promise<boolean> {
    await this.refreshTokensFromStorage();
    return !!(this.accessToken && this.refreshToken);
  }

  async getTokenInfo(): Promise<{ hasAccessToken: boolean; hasRefreshToken: boolean; accessTokenLength: number; refreshTokenLength: number }> {
    await this.refreshTokensFromStorage();
    return {
      hasAccessToken: !!this.accessToken,
      hasRefreshToken: !!this.refreshToken,
      accessTokenLength: this.accessToken?.length || 0,
      refreshTokenLength: this.refreshToken?.length || 0
    };
  }

  async forceReloadTokens(): Promise<void> {
    console.log('🔄 Force reloading tokens from storage...');
    await this.loadTokensFromStorage();
  }
}

// Create and export singleton instance
export const apiService = new ApiService();

// Search result types
export interface SearchResult {
  type: 'service' | 'category' | 'supplier' | 'product';
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  icon?: string;
  color?: string;
  route: string;
  data: any; // Original data object
}

export interface ComprehensiveSearchResults {
  services: SearchResult[];
  categories: SearchResult[];
  suppliers: SearchResult[];
  products: SearchResult[];
  total: number;
}

// Export individual service functions for convenience with proper binding
export const getServices = () => apiService.getServices();
export const getServiceByKey = (key: string) => apiService.getServiceByKey(key);
export const getCategories = () => apiService.getCategories();
export const getCategoryByKey = (key: string) => apiService.getCategoryByKey(key);
export const getSuppliers = () => apiService.getSuppliers();
export const getSuppliersByCategory = (categoryKey: string) => apiService.getSuppliersByCategory(categoryKey);
export const getSupplierById = (id: string) => apiService.getSupplierById(id);
export const getSupplierProducts = (supplierId: string) => apiService.getSupplierProducts(supplierId);

// Orders API exports
export const createOrder = (orderData: any) => apiService.createOrder(orderData);
export const getUserOrders = (params?: any) => apiService.getUserOrders(params);
export const getOrderById = (orderId: string) => apiService.getOrderById(orderId);
export const updateOrderStatus = (orderId: string, status: string, trackingUpdate?: any) =>
  apiService.updateOrderStatus(orderId, status, trackingUpdate);

// Packages API exports
export const createPackage = (packageData: any) => apiService.createPackage(packageData);
export const requestPickup = (pickupData: any) => apiService.requestPickup(pickupData);
export const getUserPackages = (params?: any) => apiService.getUserPackages(params);
export const getPackageByTrackingNumber = (trackingNumber: string) => apiService.getPackageByTrackingNumber(trackingNumber);
export const updatePackageStatus = (trackingNumber: string, status: string, trackingUpdate?: any) =>
  apiService.updatePackageStatus(trackingNumber, status, trackingUpdate);

// Helper function to detect if text contains Arabic characters
const containsArabic = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
};

// Helper function to get translated text in the appropriate language
const getTranslatedText = (translationKey: string, defaultText: string, preferredLanguage: 'en' | 'ar'): string => {
  try {
    const i18n = require('../i18n').default;
    return i18n.t(translationKey, { lng: preferredLanguage, defaultValue: defaultText });
  } catch (error) {
    console.warn('Translation failed:', error);
    return defaultText;
  }
};

// Helper function to check if text matches search term in multiple languages
const matchesSearchTerm = (text: string, searchTerm: string, translationKey?: string): boolean => {
  // Check direct text match
  if (text.toLowerCase().includes(searchTerm)) {
    return true;
  }

  // If translation key is provided, check translations
  if (translationKey) {
    try {
      // Import i18n to access translations
      const i18n = require('../i18n').default;

      // Check English translation
      const englishText = i18n.t(translationKey, { lng: 'en', defaultValue: text });
      if (englishText.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // Check Arabic translation
      const arabicText = i18n.t(translationKey, { lng: 'ar', defaultValue: text });
      if (arabicText.toLowerCase().includes(searchTerm)) {
        return true;
      }
    } catch (error) {
      console.warn('Translation check failed:', error);
    }
  }

  return false;
};

// Comprehensive search function
export const comprehensiveSearch = async (query: string): Promise<ComprehensiveSearchResults> => {
  if (!query.trim()) {
    return {
      services: [],
      categories: [],
      suppliers: [],
      products: [],
      total: 0
    };
  }

  const searchTerm = query.toLowerCase().trim();

  // Detect search language based on the query
  const isArabicSearch = containsArabic(query);
  const preferredLanguage: 'en' | 'ar' = isArabicSearch ? 'ar' : 'en';

  const results: ComprehensiveSearchResults = {
    services: [],
    categories: [],
    suppliers: [],
    products: [],
    total: 0
  };

  try {
    // Search services
    const services = await apiService.getServices();
    results.services = services
      .filter(service =>
        matchesSearchTerm(service.label, searchTerm, `services.${service.key}`) ||
        (service.description && matchesSearchTerm(service.description, searchTerm))
      )
      .map(service => ({
        type: 'service' as const,
        id: service._id,
        title: getTranslatedText(`services.${service.key}`, service.label, preferredLanguage),
        description: service.description,
        icon: service.icon,
        color: service.color,
        route: service.route,
        data: service
      }));

    // Search categories
    const categories = await apiService.getCategories();
    results.categories = categories
      .filter(category =>
        matchesSearchTerm(category.label, searchTerm, `categories.${category.key}`) ||
        (category.description && matchesSearchTerm(category.description, searchTerm))
      )
      .map(category => ({
        type: 'category' as const,
        id: category._id,
        title: getTranslatedText(`categories.${category.key}`, category.label, preferredLanguage),
        description: category.description,
        icon: category.icon,
        color: category.color,
        route: `${category.route.pathname}?category=${category.route.params.category}`,
        data: category
      }));

    // Search suppliers - use backend search for suppliers as it's more efficient
    const suppliersResponse = await apiService.getSuppliers({ search: searchTerm });

    // Also search all suppliers locally for better bilingual support
    const allSuppliersResponse = await apiService.getSuppliers();
    const localFilteredSuppliers = allSuppliersResponse.suppliers.filter(supplier =>
      matchesSearchTerm(supplier.name, searchTerm) ||
      matchesSearchTerm(supplier.category, searchTerm, `categories.${supplier.category}`) ||
      (supplier.description && matchesSearchTerm(supplier.description, searchTerm)) ||
      supplier.tags.some(tag => matchesSearchTerm(tag, searchTerm))
    );

    // Combine and deduplicate results
    const combinedSuppliers = [...suppliersResponse.suppliers];
    localFilteredSuppliers.forEach(supplier => {
      if (!combinedSuppliers.find(s => s.id === supplier.id)) {
        combinedSuppliers.push(supplier);
      }
    });

    results.suppliers = combinedSuppliers.map(supplier => ({
      type: 'supplier' as const,
      id: supplier.id,
      title: supplier.name,
      subtitle: getTranslatedText(`categories.${supplier.category}`, supplier.category, preferredLanguage),
      description: supplier.description,
      route: `/home/<USER>
      data: supplier
    }));

    // Search products across all suppliers with bilingual support
    const productResults: SearchResult[] = [];

    for (const supplier of combinedSuppliers) {
      if (supplier.products) {
        const matchingProducts = supplier.products.filter(product =>
          matchesSearchTerm(product.name, searchTerm) ||
          (product.description && matchesSearchTerm(product.description, searchTerm)) ||
          matchesSearchTerm(product.category, searchTerm)
        );

        matchingProducts.forEach(product => {
          productResults.push({
            type: 'product' as const,
            id: product.id,
            title: product.name,
            subtitle: `${supplier.name} • ${getTranslatedText(`categories.${product.category}`, product.category, preferredLanguage)}`,
            description: product.description,
            route: `/home/<USER>
            data: { ...product, supplier }
          });
        });
      }
    }

    results.products = productResults;
    results.total = results.services.length + results.categories.length + results.suppliers.length + results.products.length;

  } catch (error) {
    console.error('Error in comprehensive search:', error);
  }

  return results;
};
export const getProductById = (supplierId: string, productId: string) => apiService.getProductById(supplierId, productId);
