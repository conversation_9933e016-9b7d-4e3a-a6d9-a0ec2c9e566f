import { <PERSON><PERSON>View } from 'react-native';
import { useMyOrdersStore } from './useMyOrdersStore';
import { Text, View, YStack, XStack, Card, Separator, Button } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useState, useEffect } from 'react';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { apiService } from '../../../services/api';
import { useCurrentUserData } from '../../useCurrentUserData';
import { showErrorAlert } from '../../common/ErrorDisplay';
import { AuthCheck, AuthDebugInfo } from '../../common/AuthCheck';

export function CustomerOrdersGUI() {
  const { t } = useTranslation();
  const { orders, setOrders } = useMyOrdersStore();
  const { user } = useCurrentUserData();
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState(false);
  const [showDebug, setShowDebug] = useState(false);

  // Fetch orders from backend on component mount
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setAuthError(false);

        // Clear any existing orders first to ensure fresh data
        setOrders([]);

        // Check if user is authenticated
        if (!user) {
          console.log('User not authenticated, skipping orders fetch');
          setAuthError(true);
          setLoading(false);
          return;
        }

        console.log('Fetching orders from backend for user:', user.email);

        // Force reload tokens to ensure we have the latest ones
        await apiService.forceReloadTokens();

        // Check authentication status
        const isAuth = await apiService.isAuthenticated();
        const tokenInfo = await apiService.getTokenInfo();
        console.log('🔐 Authentication status:', { isAuth, tokenInfo });

        if (!isAuth) {
          console.log('❌ User not authenticated with valid tokens');
          setAuthError(true);
          setLoading(false);
          return;
        }

        const response = await apiService.getUserOrders();

        if (!response.success) {
          throw new Error(response.message || 'Failed to fetch orders');
        }

        const backendOrders = response.data || [];
        console.log('Received orders from backend:', backendOrders);

        // Status mapping from backend to frontend
        const statusMap: Record<string, 'Pending' | 'Preparing' | 'On the Way' | 'Delivered'> = {
          'pending': 'Pending',
          'confirmed': 'Pending',
          'preparing': 'Preparing',
          'ready': 'Preparing',
          'out_for_delivery': 'On the Way',
          'delivered': 'Delivered',
          'cancelled': 'Delivered' // Show cancelled as delivered for now
        };

        // Convert backend orders to local store format
        const formattedOrders = backendOrders.map((order: any) => ({
          id: order.orderId,
          createdAt: order.createdAt,
          items: order.items.map((item: any) => ({
            id: item.productId,
            product: {
              id: item.productId,
              name: item.productName,
              price: item.price,
              image: item.image || '',
              category: item.category || ''
            },
            qty: item.quantity,
            finalPrice: item.price * item.quantity,
            supplierId: order.supplierId,
            supplierName: order.supplierName,
            supplierCategory: order.supplierCategory || 'Restaurant'
          })),
          supplierId: order.supplierId,
          total: order.totalAmount,
          subTotal: order.totalAmount - (order.deliveryFee || 5),
          deliveryFee: order.deliveryFee || 5,
          status: statusMap[order.status] || 'Pending', // Map backend status to frontend status
          supplierRecievedMoney: order.paymentStatus === 'paid',
          address: {
            address: order.deliveryAddress?.street || '',
            lat: order.deliveryAddress?.coordinates?.lat || 32.2211,
            lng: order.deliveryAddress?.coordinates?.lng || 35.2544
          },
          driverLocation: {
            address: 'Driver location not available',
            lat: 32.2211,
            lng: 35.2544
          },
          phone: order.customerPhone || '',
          note: order.notes || '',
          paymentMethod: order.paymentMethod as 'cash' | 'card',
          promo: '',
          estimatedTime: order.estimatedDeliveryTime || '30-45 mins',
          driverName: order.driverName || 'Driver',
          driverPhone: order.driverPhone || ''
        }));

        // Update the store with formatted orders
        setOrders(formattedOrders);
      } catch (error) {
        console.error('Error fetching orders:', error);

        // Check if it's an authentication error
        if (error instanceof Error && error.message.includes('not authenticated')) {
          setAuthError(true);
        } else {
          showErrorAlert(error, t);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [user]); // Re-fetch when user changes

  const filteredOrders = selectedFilter === 'All'
    ? orders
    : orders.filter((order) => order.status === selectedFilter);

  // Show authentication required message
  if (authError || !user) {
    return (
      <AuthCheck>
        <YStack flex={1} alignItems="center" justifyContent="center" padding="$6" gap="$4">
          <AuthDebugInfo />
        </YStack>
      </AuthCheck>
    );
  }

  const filters = [
    { key: 'All', label: t('orders.filters.all', { defaultValue: 'All' }) },
    { key: 'Delivered', label: t('orders.filters.delivered', { defaultValue: 'Delivered' }) },
    { key: 'On the Way', label: t('orders.filters.onTheWay', { defaultValue: 'On the Way' }) },
    { key: 'Preparing', label: t('orders.filters.preparing', { defaultValue: 'Preparing' }) }
  ];

  return (
    <>
    <View
          width={"100%"}
          style={{
              paddingVertical: 40,
              paddingHorizontal: 24,
              borderBottomLeftRadius: 32,
              borderBottomRightRadius: 32,
              backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
              backgroundColor: '#7529B3', // fallback for platforms without gradient support
          }}
          >
          <MotiView
              from={{ opacity: 0, translateY: -20 }}
              animate={{ opacity: 1, translateY: 0 }}
          >
              <XStack justifyContent="space-between" alignItems="center" width="100%">
                <Text fontSize="$10" fontWeight="800" color="white" textAlign="center" flex={1}>
                  🧾 {t('orders.myOrders', { defaultValue: 'My Orders' })}
                </Text>
                <Button
                  size="$2"
                  variant="outlined"
                  backgroundColor="rgba(255,255,255,0.2)"
                  borderColor="rgba(255,255,255,0.3)"
                  onPress={() => setShowDebug(!showDebug)}
                >
                  <Text fontSize="$2" color="white">Debug</Text>
                </Button>
              </XStack>
          </MotiView>
      </View>
    <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}>
      
      <YStack p="$4" gap="$4" width={'95%'} alignSelf='center'>
        {/* Debug Info */}
        {showDebug && <AuthDebugInfo />}

        {/* Filter Bar */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ marginBottom: 16 }}>
          <XStack gap="$2" pl="$4" pr="$2">
            {filters.map((filter) => {
              const isActive = selectedFilter === filter.key
              return (
                <Button
                  key={filter.key}
                  size="$3"
                  br="$10"
                  px="$4"
                  py="$2"
                  bg={isActive ? '$primary' : '$color2'}
                  color={isActive ? 'white' : '$gray10'}
                  fontWeight="600"
                  onPress={() => setSelectedFilter(filter.key)}
                >
                  {filter.label}
                </Button>
              )
            })}
          </XStack>
        </ScrollView>

        {filteredOrders.length === 0 && (
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing' }}
          >
            <Text mt="$6" fontSize="$5" color="$gray10" textAlign="center">
              {t('orders.noOrdersYet', { defaultValue: 'You have no orders yet.' })}
            </Text>
          </MotiView>
        )}

        {filteredOrders.map((order, index) => (
          <MotiView
            key={order.id}
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 50 }}
          >
            <Card
              bg="$backgroundStrong"
              br="$6"
              p="$4"
              shadowColor="#000"
              shadowOffset={{ width: 0, height: 4 }}
              shadowOpacity={0.1}
              shadowRadius={6}
              borderColor="$colorTransparent"
            >
              <XStack jc="space-between" ai="center">
                <Text fontWeight="700">#{order.id}</Text>
                <Text color="$gray9" fontSize="$2">
                  {new Date(order.createdAt).toLocaleDateString()} {new Date(order.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </XStack>

              <Separator my="$2" />

              <YStack gap="$2" width={'95%'}>
                <XStack gap="$2" ai="center">
                  <Ionicons name="list-outline" size={16} color="#888" />
                  <Text color="$gray10">{t('orders.items', { defaultValue: 'Items' })}: {order.items.map(i => i.product.name).join(', ')}</Text>
                </XStack>
                <XStack gap="$2" ai="center">
                  <Ionicons name="cash-outline" size={16} color="#888" />
                  <Text color="$gray10">{t('orders.totalAmount', { defaultValue: 'Total' })}: ₪{order.total.toFixed(2)}</Text>
                </XStack>
                <XStack gap="$2" ai="center">
                  <Ionicons name="stats-chart-outline" size={16} color="#888" />
                  <Text color={
                    order.status === 'Delivered' ? '$green10' :
                    order.status === 'On the Way' ? '$orange10' :
                    '$blue10'
                  }>
                    {t('orders.orderStatus', { defaultValue: 'Status' })}: {t(`orders.statuses.${order.status.toLowerCase().replace(/\s+/g, '_')}`, { defaultValue: order.status })}
                  </Text>
                </XStack>
              </YStack>

              <Button mt="$3" size="$3" br="$6" variant="outlined" icon={<Ionicons name="eye-outline" size={18} />}
                onPress={() =>
                  router.push({
                    pathname: "/orders/order-details",
                    params: {id: order.id}
                  })
                }
              >
                {t('orders.viewDetails', { defaultValue: 'View Details' })}
              </Button>
            </Card>
          </MotiView>
        ))}
      </YStack>
    </ScrollView>
    </>
  )
}
